---
- name: Upgrade RHEL 6 to RHEL 7
  hosts: rhel6
  become: true
  gather_facts: true
  
  pre_tasks:
    - name: Ensure system is registered with Red Hat Subscription Manager
      ansible.builtin.command:
        cmd: subscription-manager status
      register: subscription_status
      failed_when: subscription_status.rc != 0
      changed_when: false

    - name: Verify system meets minimum requirements
      ansible.builtin.assert:
        that:
          - ansible_memtotal_mb >= 1024
          - ansible_distribution == "RedHat"
          - ansible_distribution_major_version == "6"
        msg: "System does not meet minimum requirements for upgrade"

    - name: Install required packages for upgrade
      ansible.builtin.yum:
        name:
          - redhat-upgrade-tool
          - preupgrade-assistant
          - preupgrade-assistant-contents
          - preupgrade-assistant-ui
        state: present

  tasks:
    - name: Run preupgrade assistant
      ansible.builtin.command:
        cmd: preupg
      register: preupg_result
      changed_when: preupg_result.rc == 0
      failed_when: preupg_result.rc > 1

    - name: Check preupgrade results
      ansible.builtin.command:
        cmd: grep -q "INPLACERISK=High" /root/preupgrade/result.html
      register: risk_check
      failed_when: risk_check.rc == 0
      changed_when: false
      ignore_errors: true

    - name: Backup important data
      ansible.builtin.command:
        cmd: tar -czf /root/pre-upgrade-backup-{{ ansible_date_time.date }}.tar.gz /etc /var/log /root/preupgrade
      changed_when: true

    - name: Download RHEL 7 upgrade image
      ansible.builtin.command:
        cmd: "redhat-upgrade-tool-cli --network {{ target_release }} --force"
      register: download_result
      changed_when: download_result.rc == 0
      failed_when: download_result.rc != 0

    - name: Reboot to start upgrade process
      ansible.builtin.reboot:
        msg: "Rebooting to start RHEL 7 upgrade process"
        reboot_timeout: 7200
      register: reboot_result

  post_tasks:
    - name: Verify upgrade completed successfully
      ansible.builtin.assert:
        that:
          - ansible_distribution == "RedHat"
          - ansible_distribution_major_version == "7"
        msg: "Upgrade to RHEL 7 failed"
      register: upgrade_verification
      ignore_errors: true

    - name: Run post-upgrade tasks
      ansible.builtin.command:
        cmd: yum -y update
      when: upgrade_verification is succeeded
      changed_when: true
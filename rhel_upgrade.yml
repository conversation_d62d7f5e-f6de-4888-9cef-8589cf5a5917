---
- name: Upgrade RHEL to target release
  hosts: all
  become: true
  gather_facts: true
  
  pre_tasks:
    - name: Ensure system is registered with Red Hat Subscription Manager
      ansible.builtin.command:
        cmd: subscription-manager status
      register: subscription_status
      failed_when: subscription_status.rc != 0
      changed_when: false

    - name: Set facts based on RHEL version
      ansible.builtin.set_fact:
        is_rhel6: "{{ ansible_distribution == 'RedHat' and ansible_distribution_major_version == '6' }}"
        is_rhel7: "{{ ansible_distribution == 'RedHat' and ansible_distribution_major_version == '7' }}"
        is_rhel8: "{{ ansible_distribution == 'RedHat' and ansible_distribution_major_version == '8' }}"
        is_rhel9: "{{ ansible_distribution == 'RedHat' and ansible_distribution_major_version == '9' }}"

    - name: Verify system meets minimum requirements for RHEL 6 to 7 upgrade
      ansible.builtin.assert:
        that:
          - ansible_memtotal_mb >= 1024
        msg: "System does not meet minimum memory requirements for upgrade"
      when: is_rhel6

    - name: Verify system meets minimum requirements for RHEL 7 to 8 upgrade
      ansible.builtin.assert:
        that:
          - ansible_memtotal_mb >= 1536
          - ansible_distribution_version is version('7.6', '>=')
        msg: "System does not meet minimum requirements for upgrade"
      when: is_rhel7

    - name: Verify system meets minimum requirements for RHEL 8 to 9 upgrade
      ansible.builtin.assert:
        that:
          - ansible_memtotal_mb >= 1536
          - ansible_distribution_version is version('8.6', '>=')
        msg: "System does not meet minimum requirements for upgrade"
      when: is_rhel8

  tasks:
    # RHEL 6 to 7 specific tasks
    - name: Install RHEL 6 to 7 upgrade packages
      ansible.builtin.yum:
        name:
          - redhat-upgrade-tool
          - preupgrade-assistant
          - preupgrade-assistant-contents
          - preupgrade-assistant-ui
        state: present
      when: is_rhel6

    - name: Run preupgrade assistant for RHEL 6
      ansible.builtin.command:
        cmd: preupg
      register: preupg_result
      changed_when: preupg_result.rc == 0
      failed_when: preupg_result.rc > 1
      when: is_rhel6

    - name: Check preupgrade results for RHEL 6
      ansible.builtin.command:
        cmd: grep -q "INPLACERISK=High" /root/preupgrade/result.html
      register: risk_check
      failed_when: risk_check.rc == 0
      changed_when: false
      ignore_errors: true
      when: is_rhel6

    - name: Download RHEL 7 upgrade image
      ansible.builtin.command:
        cmd: "redhat-upgrade-tool-cli --network {{ target_release }} --force"
      register: download_result
      changed_when: download_result.rc == 0
      failed_when: download_result.rc != 0
      when: is_rhel6

    # RHEL 7 to 8 and RHEL 8 to 9 common tasks
    - name: Install Leapp upgrade packages for RHEL 7
      ansible.builtin.yum:
        name:
          - leapp
          - leapp-repository
          - rhel-upgrade-leapp-data
        state: present
      when: is_rhel7

    - name: Install Leapp upgrade packages for RHEL 8
      ansible.builtin.dnf:
        name:
          - leapp
          - leapp-repository
          - rhel-upgrade-leapp-data
        state: present
      when: is_rhel8

    - name: Enable required repositories for RHEL 7
      ansible.builtin.command:
        cmd: subscription-manager repos --enable rhel-7-server-extras-rpms
      changed_when: true
      when: is_rhel7

    - name: Enable required repositories for RHEL 8
      ansible.builtin.command:
        cmd: subscription-manager repos --enable rhel-8-for-x86_64-appstream-rpms
      changed_when: true
      when: is_rhel8

    - name: Run leapp preupgrade check
      ansible.builtin.command:
        cmd: leapp preupgrade
      register: preupgrade_result
      changed_when: preupgrade_result.rc == 0
      failed_when: preupgrade_result.rc > 1
      ignore_errors: true
      when: is_rhel7 or is_rhel8

    - name: Check for inhibitors in preupgrade report
      ansible.builtin.command:
        cmd: grep -q "inhibitor" /var/log/leapp/leapp-report.txt
      register: inhibitor_check
      failed_when: inhibitor_check.rc == 0
      changed_when: false
      ignore_errors: true
      when: is_rhel7 or is_rhel8

    # Common backup task for all versions
    - name: Backup important data
      ansible.builtin.command:
        cmd: >
          tar -czf /root/pre-upgrade-backup-{{ ansible_date_time.date }}.tar.gz 
          /etc /var/log {{ '/root/preupgrade' if is_rhel6 else '' }}
      changed_when: true

    # Run the appropriate upgrade command
    - name: Run leapp upgrade for RHEL 7 or 8
      ansible.builtin.command:
        cmd: leapp upgrade --target {{ target_release }}
      register: upgrade_result
      changed_when: upgrade_result.rc == 0
      failed_when: upgrade_result.rc != 0
      when: is_rhel7 or is_rhel8

    # Reboot to start upgrade process
    - name: Reboot to start upgrade process
      ansible.builtin.reboot:
        msg: "Rebooting to start RHEL upgrade process"
        reboot_timeout: 7200
      register: reboot_result

  post_tasks:
    - name: Verify upgrade completed successfully
      ansible.builtin.assert:
        that:
          - ansible_distribution == "RedHat"
          - >
            (is_rhel6 and ansible_distribution_major_version == '7') or
            (is_rhel7 and ansible_distribution_major_version == '8') or
            (is_rhel8 and ansible_distribution_major_version == '9')
        msg: "Upgrade to target RHEL version failed"
      register: upgrade_verification
      ignore_errors: true

    - name: Run post-upgrade tasks for RHEL 6 to 7
      ansible.builtin.command:
        cmd: yum -y update
      when: is_rhel6 and upgrade_verification is succeeded
      changed_when: true

    - name: Run post-upgrade tasks for RHEL 7 to 8
      ansible.builtin.command:
        cmd: dnf -y update
      when: is_rhel7 and upgrade_verification is succeeded
      changed_when: true

    - name: Run post-upgrade tasks for RHEL 8 to 9
      ansible.builtin.dnf:
        name: "*"
        state: latest
      when: is_rhel8 and upgrade_verification is succeeded
# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is an Ansible project for automated RHEL (Red Hat Enterprise Linux) in-place upgrades using the Leapp framework and redhat-upgrade-tool. It supports upgrading RHEL 6→7, RHEL 7→8, and RHEL 8→9.

## Common Commands

### Running Playbooks
```bash
# Run main unified upgrade playbook
ansible-playbook rhel_upgrade.yml

# Run specific version upgrade playbooks
ansible-playbook rhel6_to_rhel7_upgrade.yml
ansible-playbook rhel7_to_rhel8_upgrade.yml
ansible-playbook rhel8_to_rhel9_upgrade.yml

# Run with specific inventory or limit to hosts
ansible-playbook rhel_upgrade.yml -l rhel8
ansible-playbook rhel_upgrade.yml -i custom_inventory.yml

# Check mode (dry run)
ansible-playbook rhel_upgrade.yml --check

# Verbose output
ansible-playbook rhel_upgrade.yml -vvv
```

### Ansible Management
```bash
# Install required collections
ansible-galaxy collection install -r requirements.yml

# Check syntax
ansible-playbook rhel_upgrade.yml --syntax-check

# List hosts
ansible-inventory --list

# Test connectivity
ansible all -m ping
```

## Architecture

### Playbook Structure
- **rhel_upgrade.yml**: Main unified playbook that detects RHEL version and runs appropriate upgrade tasks
- **rhel6_to_rhel7_upgrade.yml**: RHEL 6→7 upgrade using redhat-upgrade-tool and preupgrade-assistant
- **rhel7_to_rhel8_upgrade.yml**: RHEL 7→8 upgrade using Leapp framework
- **rhel8_to_rhel9_upgrade.yml**: RHEL 8→9 upgrade using Leapp framework

### Inventory Design
The inventory.yml defines host groups by RHEL version (rhel6, rhel7, rhel8) with version-specific variables:
- Each group has a `target_release` variable defining the upgrade target
- Global variables like `satellite_url` for Red Hat Satellite integration

### Upgrade Approaches
1. **RHEL 6→7**: Uses redhat-upgrade-tool with preupgrade-assistant for compatibility checking
2. **RHEL 7→8 and 8→9**: Uses Leapp framework with preupgrade checks and inhibitor detection
3. All upgrades include automatic backups and post-upgrade verification

### Key Components
- Pre-upgrade system requirement validation (memory, version, subscription status)
- Backup creation before upgrade initiation
- Post-upgrade verification and system updates
- Long reboot timeouts (7200s) to accommodate upgrade process

### Configuration
- ansible.cfg configures privilege escalation, inventory location, and Red Hat Automation Hub access
- Authentication tokens for Red Hat content collections are configured in ansible.cfg
- Host key checking disabled for lab environments
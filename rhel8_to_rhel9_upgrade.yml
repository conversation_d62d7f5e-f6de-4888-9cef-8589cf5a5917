---
- name: Upgrade RHEL 8 to RHEL 9
  hosts: rhel8
  become: true
  become_flags: '-r unconfined_r -t unconfined_t'
  gather_facts: true

  pre_tasks:
    - name: Ensure system is registered with Red Hat Subscription Manager
      ansible.builtin.command:
        cmd: subscription-manager status
      register: subscription_status
      failed_when: subscription_status.rc != 0
      changed_when: false

    - name: Verify system meets minimum requirements
      ansible.builtin.assert:
        that:
          - ansible_memtotal_mb >= 3072
          - ansible_distribution == "RedHat"
          - ansible_distribution_version == "8.10"
        msg: "System does not meet minimum requirements for upgrade"

    - name: Ensure current repositories are enabled
      community.general.rhsm_repository:
        name:
          - rhel-8-for-x86_64-baseos-rpms
          - rhel-8-for-x86_64-appstream-rpms
        state: enabled

    - name: Ensure system release version is set
      community.general.rhsm_release:
        release: "{{ ansible_distribution_version }}"
      changed_when: true

    - name: Check if versionlock is installed
      ansible.builtin.dnf:
        name: python3-dnf-plugin-versionlock
        state: present
      check_mode: true
      register: versionlock_check

    - name: Ensure versionlock is cleared if package is installed
      community.general.dnf_versionlock:
        state: clean
      when: not versionlock_check.changed

    - name: Install required packages for upgrade
      ansible.builtin.dnf:
        name: leapp-upgrade
        state: present

    - name: Ensure all packages are updated
      ansible.builtin.dnf:
        name: "*"
        state: latest
        update_only: true
      register: update_result

    - name: Reboot after updating packages
      meta
      when: update_result.changed

  tasks:
    - name: Run leapp preupgrade check
      ansible.builtin.command:
        cmd: leapp preupgrade --target {{ target_release }}
      register: preupgrade_result
      changed_when: preupgrade_result.rc == 0
      failed_when: preupgrade_result.rc > 1
      ignore_errors: true

    - name: Check for inhibitors in preupgrade report
      ansible.builtin.command:
        cmd: grep -q "inhibitor" /var/log/leapp/leapp-report.txt
      register: inhibitor_check
      failed_when: inhibitor_check.rc == 0
      changed_when: false
      ignore_errors: true

    - name: Backup important data
      community.general.archive:
        path:
          - /etc
          - /var/log
        dest: /root/pre-upgrade-backup-{{ ansible_date_time.date }}.tar.gz
        format: gz
        owner: root
        group: root
        mode: '0600'
      register: backup_result
      changed_when: backup_result.changed

    - name: Run leapp upgrade
      ansible.builtin.command:
        cmd: leapp upgrade
      register: upgrade_result
      changed_when: upgrade_result.rc == 0
      failed_when: upgrade_result.rc != 0

    - name: Reboot to start upgrade process
      ansible.builtin.reboot:
        msg: "Rebooting to start RHEL 9 upgrade process"
        reboot_timeout: 7200
      register: reboot_result

  post_tasks:
    - name: Verify upgrade completed successfully
      ansible.builtin.assert:
        that:
          - ansible_distribution == "RedHat"
          - ansible_distribution_major_version == "9"
        msg: "Upgrade to RHEL 9 failed"
      register: upgrade_verification
      ignore_errors: true

    - name: Run post-upgrade tasks
      ansible.builtin.dnf:
        name: "*"
        state: latest
        update_only: true
      when: upgrade_verification is succeeded

  handlers:
    - name: Reboot after updating packages
      ansible.builtin.reboot:
        msg: "Rebooting to apply package updates"
        reboot_timeout: 7200

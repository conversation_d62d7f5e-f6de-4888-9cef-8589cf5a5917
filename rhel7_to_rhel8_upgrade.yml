---
- name: Upgrade RHEL 7 to RHEL 8
  hosts: rhel7
  become: true
  gather_facts: true
  
  pre_tasks:
    - name: Ensure system is registered with Red Hat Subscription Manager
      ansible.builtin.command:
        cmd: subscription-manager status
      register: subscription_status
      failed_when: subscription_status.rc != 0
      changed_when: false

    - name: Verify system meets minimum requirements
      ansible.builtin.assert:
        that:
          - ansible_memtotal_mb >= 1536
          - ansible_distribution == "RedHat"
          - ansible_distribution_major_version == "7"
          - ansible_distribution_version is version('7.6', '>=')
        msg: "System does not meet minimum requirements for upgrade"

    - name: Install required packages for upgrade
      ansible.builtin.yum:
        name:
          - leapp
          - leapp-repository
          - rhel-upgrade-leapp-data
        state: present

    - name: Enable required repositories
      ansible.builtin.command:
        cmd: subscription-manager repos --enable rhel-7-server-extras-rpms
      changed_when: true

  tasks:
    - name: Run leapp preupgrade check
      ansible.builtin.command:
        cmd: leapp preupgrade
      register: preupgrade_result
      changed_when: preupgrade_result.rc == 0
      failed_when: preupgrade_result.rc > 1
      ignore_errors: true

    - name: Check for inhibitors in preupgrade report
      ansible.builtin.command:
        cmd: grep -q "inhibitor" /var/log/leapp/leapp-report.txt
      register: inhibitor_check
      failed_when: inhibitor_check.rc == 0
      changed_when: false
      ignore_errors: true

    - name: Backup important data
      ansible.builtin.command:
        cmd: tar -czf /root/pre-upgrade-backup-{{ ansible_date_time.date }}.tar.gz /etc /var/log
      changed_when: true

    - name: Run leapp upgrade
      ansible.builtin.command:
        cmd: leapp upgrade --target {{ target_release }}
      register: upgrade_result
      changed_when: upgrade_result.rc == 0
      failed_when: upgrade_result.rc != 0

    - name: Reboot to start upgrade process
      ansible.builtin.reboot:
        msg: "Rebooting to start RHEL 8 upgrade process"
        reboot_timeout: 7200
      register: reboot_result

  post_tasks:
    - name: Verify upgrade completed successfully
      ansible.builtin.assert:
        that:
          - ansible_distribution == "RedHat"
          - ansible_distribution_major_version == "8"
        msg: "Upgrade to RHEL 8 failed"
      register: upgrade_verification
      ignore_errors: true

    - name: Run post-upgrade tasks
      ansible.builtin.command:
        cmd: dnf -y update
      when: upgrade_verification is succeeded
      changed_when: true